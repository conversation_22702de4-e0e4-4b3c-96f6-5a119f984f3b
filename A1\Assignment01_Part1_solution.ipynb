import numpy as np
import matplotlib.pyplot as plt

def elu(x, alpha=0.1):
    x = np.array(x)
    y = np.where(x <= 0, alpha*(np.exp(x)-1), x)
    return y

def delu(x, alpha=0.1):
    x = np.array(x)
    dy = np.where(x < 0, alpha*np.exp(x), 1.0)
    return dy

xs = np.linspace(-5, 5, 400)
ys = elu(xs, alpha=0.1)
dys = delu(xs, alpha=0.1)

plt.figure()
plt.plot(xs, ys)
plt.title("ELU (alpha=0.1)")
plt.xlabel("x")
plt.ylabel("ELU(x)")
plt.grid(True)
plt.show()

plt.figure()
plt.plot(xs, dys)
plt.title("Derivative of ELU (alpha=0.1)")
plt.xlabel("x")
plt.ylabel("d/dx ELU(x)")
plt.grid(True)
plt.show()


import numpy as np
import matplotlib.pyplot as plt
from scipy.special import erf

Phi = lambda x: 0.5 * (1 + erf(x / np.sqrt(2)))
phi = lambda x: np.exp(-x**2 / 2) / np.sqrt(2*np.pi)

x = np.linspace(-5, 5, 800)
gelu = x * Phi(x)
dgelu = Phi(x) + x * phi(x)


plt.figure(); plt.plot(x, gelu); plt.title("GELU"); plt.grid(True)
plt.figure(); plt.plot(x, dgelu); plt.title("d/dx GELU"); plt.grid(True)
plt.show()


import numpy as np

# ----- data -----
x = np.array([1.2, -1.0, 2.0])   # shape (3,)
y = 2  # 1-indexed, ground-truth class = 2

# ----- layer 1 -----
W1 = np.array([
    [ 1, -1,  1, -1,  1],
    [-1,  1, -1,  1, -1],
    [ 2,  2,  2, -2, -2],
], dtype=float)                   # shape (3,5)
b1 = np.array([0, 1, 0, 1, 0], float)

z1 = x @ W1 + b1                  # pre-activation
h1 = np.maximum(z1, 0)            # ReLU
print("h1(x) =", h1)              # -> [6.2, 2.8, 6.2, 0. , 0. ]

# ----- layer 2 -----
# rows 4-5 won't affect result since h1[3]=h1[4]=0
W2 = np.array([
    [-1,  1, -1],
    [ 1,  1,  1],
    [-1,  1,  1],
    [ 1, -1,  1],   # arbitrary (won't contribute)
    [-1, -1,  2],   # arbitrary (won't contribute)
], dtype=float)                   # shape (5,3)
b2 = np.array([1, 0, 1], float)

z2 = h1 @ W2 + b2
h2 = np.maximum(z2, 0)
print("h2(x) =", h2)              # -> [0. , 15.2, 3.8]

# ----- output (logits) -----
W3 = np.array([
    [ 2, -2],
    [-2,  2],
    [ 2,  2],
], dtype=float)                   # shape (3,2)
b3 = np.array([1.0, 1.5], float)

h3 = h2 @ W3 + b3                 # logits
print("h3(x) =", h3)              # -> [-21.8, 39.5]

# ----- softmax -----
def softmax(z):
    z = z - np.max(z)             # numerically stable
    e = np.exp(z)
    return e / e.sum()

p = softmax(h3)
print("p(x) =", p)                # -> [~2.386e-27, ~1 - 2.386e-27]

# ----- prediction & CE loss -----
y_hat = 1 + np.argmax(p)          # back to 1-indexed
print("y_hat =", y_hat)

ce = -np.log(p[y-1])              # cross-entropy with one-hot label
print("CE(1_y, p(x)) =", ce)      # -> ~2.386e-27 (≈ 0)


import torch
student_id = 32755015
torch.manual_seed(student_id)

import torch.nn.functional as F

# ----- data from figure -----
X = torch.tensor([[-1.0,  2.0, 1.0],
                  [ 1.5,  0.5, 0.5],
                  [ 2.0, -1.0, 4.0],
                  [-1.0,  1.0, 0.5]], dtype=torch.float32)   # (B=4, 3)
y = torch.tensor([1, 2, 1, 3], dtype=torch.long)            # 1-based
y0 = y - 1                                                  # -> 0-based for PyTorch CE

W1 = torch.randn(3, 5, requires_grad=True)    # 3x5
b1 = torch.randn(5,   requires_grad=True)     # 5
W2 = torch.randn(5, 3, requires_grad=True)    # 5x3
b2 = torch.randn(3,   requires_grad=True)     # 3

alpha = 0.1
lr = 0.1

# === Forward ===
Z1 = X @ W1 + b1         # (4,5)
H1 = torch.where(Z1 > 0, Z1, alpha*(torch.exp(Z1)-1))  # ELU alpha=0.1
Z2 = H1 @ W2 + b2        # (4,3)
P  = F.softmax(Z2, dim=1)
loss = F.cross_entropy(Z2, y0)   # CE loss

print("P (softmax):\n", P.detach().numpy())
print("Loss (CE):", loss.item())

# === Backward via autograd ===
loss.backward()

auto_dW2 = W2.grad.detach().clone()
auto_db2 = b2.grad.detach().clone()
auto_dW1 = W1.grad.detach().clone()
auto_db1 = b1.grad.detach().clone()

# === Clear gradients ===
W1.grad.zero_(); b1.grad.zero_(); W2.grad.zero_(); b2.grad.zero_()

# === Manual gradients (batch-mean) ===
B = X.shape[0]
with torch.no_grad():
    # forward
    Z1_ = X @ W1 + b1
    H1_ = torch.where(Z1_ > 0, Z1_, alpha*(torch.exp(Z1_)-1))
    Z2_ = H1_ @ W2 + b2
    P_  = F.softmax(Z2_, dim=1)

    # dZ2 = (P - onehot)/B
    onehot = torch.zeros_like(P_)
    onehot[torch.arange(B), y0] = 1.0
    dZ2 = (P_ - onehot) / B                         # (4,3)

    dW2 = H1_.T @ dZ2                               # (5,3)
    db2 = dZ2.sum(dim=0)                            # (3,)

    # back to layer 1
    dH1 = dZ2 @ W2.T                                # (4,5)
    dELU = torch.where(Z1_ > 0, torch.ones_like(Z1_), alpha*torch.exp(Z1_))
    dZ1 = dH1 * dELU                                # (4,5)

    dW1 = X.T @ dZ1                                 # (3,5)
    db1 = dZ1.sum(dim=0)                            # (5,)

# === Compare manual vs autograd ===
def rel_err(a, b):  # relative error helper
    return (a - b).abs().max().item() / (b.abs().max().item() + 1e-12)

print("\n[Gradient check: max relative error]")
print("W2:", rel_err(dW2, auto_dW2))
print("b2:", rel_err(db2, auto_db2))
print("W1:", rel_err(dW1, auto_dW1))
print("b1:", rel_err(db1, auto_db1))

# === One SGD step ===
with torch.no_grad():
    W2 -= lr * dW2; b2 -= lr * db2
    W1 -= lr * dW1; b1 -= lr * db1

# === Check new loss ===
Z1 = X @ W1 + b1
H1 = torch.where(Z1 > 0, Z1, alpha*(torch.exp(Z1)-1))
Z2 = H1 @ W2 + b2
new_loss = F.cross_entropy(Z2, y0)
print("\nLoss after 1 SGD step:", new_loss.item())


# input data
X = torch.tensor([
    [-1,  2,  1],
    [ 1.5, 0.5, 0.5],
    [ 2, -1,  4],
    [-1,  1, 0.5]
])

# random weights
W1 = torch.randn(3, 5)   # (input_dim=3, hidden_dim=5)
b1 = torch.randn(1, 5)   # (1, hidden_dim=5)

# forward prop
Z1 = X @ W1 + b1   # (batch_size=4, hidden_dim=5)

print("Z^1 =", Z1)


alpha = 0.1
h1 = torch.where(Z1 > 0, Z1, alpha * (torch.exp(Z1) - 1))
print("h^1(x) =", h1)

# Forward pass for the second hidden layer and output
Z2 = h1 @ W2 + b2      # Pre-activation of the second layer
P = torch.softmax(Z2, dim=1)   # Softmax to get prediction probabilities

# Predicted labels (argmax)
y_hat = torch.argmax(P, dim=1) + 1   # +1 if class index starts from 1

print("Z^2 =", Z2)
print("P(x) =", P)
print("Predicted labels (y_hat) =", y_hat)


# ground truth labels (1-based)
y0 = y - 1  # convert to 0-based class indices for PyTorch

# Method 1 (recommended, numerically stable): from logits directly
loss_ce = F.cross_entropy(Z2, y0)      # average over batch
print("Cross-Entropy loss (from logits) =", loss_ce.item())

# Method 2 (for verification): using softmax probabilities
P = torch.softmax(Z2, dim=1)           # probabilities
loss_ce_manual = (-torch.log(P[torch.arange(P.size(0)), y0])).mean()
print("Cross-Entropy loss (from probabilities) =", loss_ce_manual.item())


B = Z2.size(0)                     # batch size
y0 = (y - 1).long()                # 0-based targets if your y is 1-based

# dℓ/dh² : gradient w.r.t. logits (softmax + CE, batch mean)
P = torch.softmax(Z2, dim=1)       # probabilities
onehot = torch.zeros_like(P)
onehot[torch.arange(B), y0] = 1.0
d_h2 = (P - onehot) / B            # == ∂ℓ/∂h²

# dℓ/dW² and dℓ/db²
dW2 = h1.T @ d_h2                  # (hidden_dim x num_classes)
db2 = d_h2.sum(dim=0)              # (num_classes,)

print("∂ℓ/∂h² (shape):", d_h2.shape)
print("∂ℓ/∂W² (shape):", dW2.shape)
print("∂ℓ/∂b² (shape):", db2.shape)


# Backprop to the first layer
alpha = 0.1

# 1) dℓ/dh¹  = dℓ/dh² · (W²)ᵀ
d_h1 = d_h2 @ W2.T                         # shape: (B, 5)

# 2) through ELU: dℓ/dẗh¹ = dℓ/dh¹ ⊙ ELU'(Z¹)
# ELU'(z) = 1 if z>0, else alpha*exp(z)
dELU = torch.where(Z1 > 0, torch.ones_like(Z1), alpha * torch.exp(Z1))
d_z1 = d_h1 * dELU                         # this is ∂ℓ/∂ẗh¹ (a.k.a. ∂ℓ/∂Z¹)

# 3) parameter gradients of the first layer
dW1 = X.T @ d_z1                           # shape: (3, 5)
db1 = d_z1.sum(dim=0)                      # shape: (5,)

print("∂ℓ/∂h¹ shape:", d_h1.shape)
print("∂ℓ/∂ẗh¹ (== ∂ℓ/∂Z¹) shape:", d_z1.shape)
print("∂ℓ/∂W¹ shape:", dW1.shape)
print("∂ℓ/∂b¹ shape:", db1.shape)


# learning rate
eta = 0.01

# update parameters
W2_new = W2 - eta * dW2
b2_new = b2 - eta * db2

W1_new = W1 - eta * dW1
b1_new = b1 - eta * db1

print("Updated W2 =", W2_new)
print("Updated b2 =", b2_new)
print("Updated W1 =", W1_new)
print("Updated b1 =", b1_new)


import torch
from torch.utils.data import DataLoader
from torchvision import datasets, transforms

transform = transforms.Compose([
    transforms.ToTensor(),  # Convert the image to a tensor with shape [C, H, W]
    transforms.Normalize((0.5,), (0.5,)),  # Normalize to [-1, 1]
    transforms.Lambda(lambda x: x.view(28*28)) # Flatten the tensor to shape [-1,HW]
])

# Load the MNIST dataset
train_dataset = datasets.MNIST(root='./data', train=True, download=True, transform=transform)
test_dataset = datasets.MNIST(root='./data', train=False, download=True, transform=transform)

train_data, train_labels = train_dataset.data, train_dataset.targets
test_data, test_labels = test_dataset.data, test_dataset.targets
print(train_data.shape, train_labels.shape)
print(test_data.shape, test_labels.shape)

train_dataset.data = train_data.data.reshape(-1, 28*28)
test_dataset.data = test_data.data.reshape(-1, 28*28)

train_data, train_labels = train_dataset.data, train_dataset.targets
test_data, test_labels = test_dataset.data, test_dataset.targets
print(train_data.shape, train_labels.shape)
print(test_data.shape, test_labels.shape)

train_loader = DataLoader(dataset=train_dataset, batch_size=64, shuffle=True)
test_loader = DataLoader(dataset=test_dataset, batch_size=64, shuffle=False)

class MyLinear(torch.nn.Module):
  def __init__(self, input_size, output_size):
    """
    input_size: the size of the input
    output_size: the size of the output
    """
    super().__init__()
    # weight: [D_in, D_out], bias: [D_out]
    self.W = nn.Parameter(torch.empty(input_size, output_size))
    self.b = nn.Parameter(torch.zeros(output_size))

    # He/Kaiming init works well with ReLU/ELU activations
    nn.init.kaiming_uniform_(self.W, a=0.0, mode="fan_in", nonlinearity="relu")
    # Optional: match PyTorch nn.Linear's default bias init
    fan_in = input_size
    bound = 1 / math.sqrt(fan_in) if fan_in > 0 else 0
    nn.init.uniform_(self.b, -bound, bound)

  #forward propagation
  def forward(self, x): #x is a mini-batch
    #Your code here
    return x @ self.W + self.b